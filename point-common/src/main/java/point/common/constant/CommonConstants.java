package point.common.constant;

import java.math.BigDecimal;
import java.util.List;

public final class CommonConstants {

    public CommonConstants() {}

    public static final String MASTER_TRANSACTION_MANAGER = "masterTransactionManager";
    public static final String ERROR_CODE = "error_code";
    public static final String ERROR_MESSAGE = "error_message";

    public static final String POST = "POST";
    public static final String GET = "GET";

    public static final String USER_ID = "userId";
    public static final String EMAIL = "email";
    public static final String USER_INFO_ID = "userInfoId";
    public static final String STATUS = "status";
    public static final String KYC_STATUS = "kycStatus";

    public static final String CONTENT_TYPE = "Content-Type";
    public static final String ACCEPT = "Accept";

    public static final String REFERENCE_ID = "reference_id";
    public static final String NAME = "name";
    public static final String BIRTHDAY = "birthday";
    public static final String ADDRESS = "address";
    public static final String JOB = "job";
    public static final String PURPOSE = "purpose";
    public static final String APPLICANT_ID = "applicant_id";
    public static final String EKYC_URL = "ekycUrl";
    public static final String API_AUTH_KEY = "apiAuthKey";
    public static final String APPLY_NO = "applyNo";
    public static final String ENTERPRISE_ID = "enterpriseId";
    public static final String TRANSITION_URL = "transitionUrl";
    public static final String ACCEPTED_AT = "accepted_at";
    public static final String X_SIGNATURE = "x-kyc-signature";
    public static final String X_TIME = "x-kyc-time";
    public static final String AUTHORIZATION_VAL_PREFIX = "Token token=";
    public static final String AUTHORIZATION = "Authorization";
    public static final String DOC_TYPE = "docType";
    public static final String DEFAULT_DOC_TYPE = "00";

    public static final String EKYC_RESULT = "ekyc_result";
    public static final String FRONT_MATCH = "front_match";
    public static final String THICKNESS_MATCH = "thickness_match";
    public static final String LIVE_MATCH = "live_match";
    public static final String MASKED_IMAGES = "masked_images";
    public static final String APPLICANT_IMAGES = "applicant_images";
    public static final String MATCH = "match";
    public static final String UNMATCH = "unmatch";
    public static final String RESULT = "result";
    public static final String VERIFICATION_ERRORS = "verification_errors";
    public static final String REDIS_USER_TOCKN_PREFIX = "user:jwt:";

    public static final String authorizationTokenKey =
            "point.common.config.GmoAuthorizationTokenConfiguration";
    public static final String CHOICE_VOTE_CONFIGURATION =
            "point.common.config.ChoiceVoteConfiguration";

    public static final String RIGHT_BRACKETS = ")";

    public static final String GMO_TOKEN_PRE_FIX = "Basic ";

    public static final String GMO_HEADER_TOKEN_KEY = "x-access-token";
    public static final String GRANT_TYPE = "grant_type";

    public static final String AUTHORIZATION_CODE_PRE_FIX = "code";

    public static final String REDIRECT_URI = "redirect_uri";

    public static final String CLIENT_ID = "client_id";

    public static final String CLIENT_SECRET = "client_secret";

    public static final String REFRESH_TOKEN = "refresh_token";

    public static final String AUTHORIZATION_CODE = "authorization_code";

    public static final String GMO_CORPORATE_KEY = "gmo:corpora:info";

    public static final String SPACE = " ";

    public static final String GMO_STG_ACCOUNT_NUMBER = "1014029";

    public static final String GMO_PRD_ACCOUNT_NUMBER = "1149414";

    public static final String APP = "APP";
    public static final String WORKER = "WORKER";

    public static final String EMPTY = "";

    public static final String TRADE_TYPE_POS = "INVEST";

    public static final BigDecimal HUNDRED = new BigDecimal("100");

    public static final String ID_TYPE = "id_type";

    public static final String PARTNER_ID = "partner_id";

    public static final String PARTNER = "partner";

    public static final String ID = "id";

    public static final String PONTA_SECRET_CODE = "w";
    public static final String PONTA_ERROR_CODE = "e";
    public static final String PONTA_PARTNER_NUMBER = "pn";
    public static final String PONTA_MEMBER_ID = "mid";
    public static final String PONTA_LOGIN_OTP_PREFIX = "ponta:login:otp:";
    public static final String UID = "uid";
    public static final String OTP = "otp";
    public static final String SOURCE = "source";
    public static final String CLIENT = "client";
    public static final String SOURCE_INVEST = "invest";
    public static final String SOURCE_OPERATE = "operate";
    public static final String a = "a";
    public static final String p = "p";
    public static final String q = "q";
    public static final String ACCESS_TOKEN = "accessToken";
    public static final String GAME = "game";
    public static final String ERROR = "error";
    public static final String PONTA_ERROR_USER_CANCEL_CONSENT_CODE = "E154000999";

    public static final int SCALE = 2;

    public static final List<String> OPTIONS = List.of("A", "B", "C", "D");
    public static final String COLON = ":";
    public static final String COMMA = ",";
}
