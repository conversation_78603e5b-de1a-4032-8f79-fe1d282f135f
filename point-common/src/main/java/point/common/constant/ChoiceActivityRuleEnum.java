package point.common.constant;

import lombok.Getter;

/** ChoiceActivityRuleEnum */
@Getter
public enum ChoiceActivityRuleEnum {

    // -- THE ID HARDCODE HERE THAT MUST BE THE SAME AS IN THE DATABASE --
    LOGIN_OPERATION_COURSE(1L, "運用コースにログインする"),
    LOGIN_INVESTMENT_COURSE(2L, "投資コースにログインする"),
    PASS_QUIZ(3L, "クイズに正解する"),
    OPEN_INVESTMENT_ACCOUNT(4L, "投資コースで口座開設を完了させる"),
    ELECTION_RESULTS_OF_VOTE(5L, "Pontaビットコイン投票で当選結果を投稿する"),
    CROP_HARVESTED_SHARED(6L, "Pontaビットコin牧場で収穫した作物をシェアする"),
    BUY_SELL_TRADE(7L, "売買取引を行う"),
    RAISE_MONSTER_LEVEL_1(8L, "Pontaファームでモンスターを育成する→レベル1"),
    RAISE_MONSTER_LEVEL_2(9L, "Pontaファームでモンスターを育成する→レベル2"),
    RAISE_MONSTER_LEVEL_3(10L, "Pontaファームでモンスターを育成する→レベル3"),
    RAISE_MONSTER_LEVEL_4(11L, "Pontaファームでモンスターを育成する→レベル4"),
    RAISE_MONSTER_LEVEL_5(12L, "Pontaファームでモンスターを育成する→レベル5"),
    RAISE_MONSTER_LEVEL_6(13L, "Pontaファームでモンスターを育成する→レベル6"),
    RAISE_MONSTER_LEVEL_7(14L, "Pontaファームでモンスターを育成する→レベル7"),
    RAISE_MONSTER_LEVEL_8(15L, "Pontaファームでモンスターを育成する→レベル8"),
    RAISE_MONSTER_LEVEL_9(16L, "Pontaファームでモンスターを育成する→レベル9"),
    RAISE_MONSTER_LEVEL_10(17L, "Pontaファームでモンスターを育成する→レベル10"),
    RAISE_MONSTER_LEVEL_11(18L, "Pontaファームでモンスターを育成する→レベル11"),
    RAISE_MONSTER_LEVEL_12(19L, "Pontaファームでモンスターを育成する→レベル12"),
    RAISE_MONSTER_LEVEL_13(20L, "Pontaファームでモンスターを育成する→レベル13"),
    RAISE_MONSTER_LEVEL_14(21L, "Pontaファームでモンスターを育成する→レベル14"),
    RAISE_MONSTER_LEVEL_15(22L, "Pontaファームでモンスターを育成する→レベル15"),
    RAISE_MONSTER_LEVEL_16(23L, "Pontaファームでモンスターを育成する→レベル16"),
    RAISE_MONSTER_LEVEL_17(24L, "Pontaファームでモンスターを育成する→レベル17"),
    RAISE_MONSTER_LEVEL_18(25L, "Pontaファームでモンスターを育成する→レベル18"),
    RAISE_MONSTER_LEVEL_19(26L, "Pontaファームでモンスターを育成する→レベル19"),
    RAISE_MONSTER_LEVEL_20(27L, "Pontaファームでモンスターを育成する→レベル20"),
    RAISE_MONSTER_LEVEL_21(28L, "Pontaファームでモンスターを育成する→レベル21"),
    RAISE_MONSTER_LEVEL_22(29L, "Pontaファームでモンスターを育成する→レベル22"),
    RAISE_MONSTER_LEVEL_23(30L, "Pontaファームでモンスターを育成する→レベル23"),
    RAISE_MONSTER_LEVEL_24(31L, "Pontaファームでモンスターを育成する→レベル24"),
    RAISE_MONSTER_LEVEL_25(32L, "Pontaファームでモンスターを育成する→レベル25"),
    RAISE_MONSTER_LEVEL_26(33L, "Pontaファームでモンスターを育成する→レベル26"),
    RAISE_MONSTER_LEVEL_27(34L, "Pontaファームでモンスターを育成する→レベル27"),
    RAISE_MONSTER_LEVEL_28(35L, "Pontaファームでモンスターを育成する→レベル28"),
    RAISE_MONSTER_LEVEL_29(36L, "Pontaファームでモンスターを育成する→レベル29"),
    RAISE_MONSTER_LEVEL_30(37L, "Pontaファームでモンスターを育成する→レベル30"),

    // -- WITHOUT DATABASE USE FOR CHOICE CONSUMPTION
    // -- NOTE: Please keep it don't conflict with the database id
    CHOICE_CONSUMPTION(100L, "投票で消費");

    private final Long id;
    private final String name;

    ChoiceActivityRuleEnum(Long id, String name) {
        this.id = id;
        this.name = name;
    }
}
