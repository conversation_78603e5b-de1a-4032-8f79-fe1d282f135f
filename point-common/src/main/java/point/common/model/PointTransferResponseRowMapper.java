package point.common.model;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import point.common.model.response.PointTransferResponse;

/**
 * RowMapper for mapping combined query results from point_transfer and choice_p_withdrawal tables
 * to PointTransferResponse objects
 */
public class PointTransferResponseRowMapper implements RowMapper<PointTransferResponse> {

    @Override
    public PointTransferResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        return PointTransferResponse.builder()
                .id(rs.getLong("id"))
                .userId(rs.getLong("user_id"))
                .userIdType(rs.getString("user_id_type"))
                .transferType(rs.getString("transfer_type"))
                .amount(rs.getBigDecimal("amount"))
                .status(rs.getString("status"))
                .requestTime(rs.getTimestamp("request_time"))
                .transferTime(rs.getTimestamp("transfer_time"))
                .createdAt(rs.getTimestamp("created_at"))
                .build();
    }
}
