package point.common.predicate;

import javax.persistence.criteria.*;
import javax.persistence.metamodel.SingularAttribute;
import org.springframework.stereotype.Component;
import point.common.entity.*;

@Component
public class UserMonsterInfoPredicate extends EntityPredicate<UserMonsterInfo> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<UserMonsterInfo> root, Long userId) {
        return criteriaBuilder.equal(root.get(UserMonsterInfo_.userId), userId);
    }

    public Predicate equalIdType(
            CriteriaBuilder criteriaBuilder, Root<UserMonsterInfo> root, String idType) {
        return criteriaBuilder.equal(root.get(UserMonsterInfo_.idType), idType);
    }

    public Predicate equalMonsterName(
            CriteriaBuilder criteriaBuilder, Root<UserMonsterInfo> root, String monsterName) {
        SingularAttribute<UserMonsterInfo, MonsterBase> monsterBaseAttr =
                UserMonsterInfo_.monsterBase;
        SingularAttribute<MonsterBase, String> monsterNameAttr = MonsterBase_.name;
        Join<UserMonsterInfo, MonsterBase> monsterJoin = root.join(monsterBaseAttr);
        Path<String> monsterNamePath = monsterJoin.get(monsterNameAttr);
        return criteriaBuilder.equal(monsterNamePath, monsterName);
    }

    public Predicate equalLevel(
            CriteriaBuilder criteriaBuilder, Root<UserMonsterInfo> root, Integer level) {
        return criteriaBuilder.equal(root.get(UserMonsterInfo_.level), level);
    }
}
