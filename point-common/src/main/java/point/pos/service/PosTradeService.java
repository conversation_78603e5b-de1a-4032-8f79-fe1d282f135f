package point.pos.service;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.util.Pair;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import point.common.component.*;
import point.common.constant.*;
import point.common.constant.Currency;
import point.common.entity.Symbol;
import point.common.entity.Trade_;
import point.common.model.response.MonsterCropsEatenResponse;
import point.common.model.response.PageData;
import point.common.util.FormatUtil;
import point.pos.entity.PosTrade;
import point.pos.entity.PosTrade_;
import point.pos.model.PosTradeRowMapper;

@Slf4j
@Service
@RequiredArgsConstructor
public class PosTradeService extends PosTradeHistoryService {

    @Autowired protected HistoricalTransactionManager historicalTransactionManager;

    @Autowired private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Qualifier("historicalNamedParameterJdbcTemplate")
    private final NamedParameterJdbcTemplate historicalJdbcTemplate;

    @Override
    public Class<PosTrade> getEntityClass() {
        return PosTrade.class;
    }

    @Override
    public Class<PosTradeRowMapper> getRowMapperClass() {
        return PosTradeRowMapper.class;
    }

    public PosTrade findFromHistoryByOrder(Long userId, Long symbolId, Long id) {
        if (symbolId == null && id == null) {
            return new PosTrade();
        }

        StringBuilder sql = new StringBuilder("SELECT * FROM pos_trade WHERE 1=1");
        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();

        if (userId != null) {
            sql.append(" AND user_id = :userId");
            mapSqlParameterSource.addValue("userId", userId);
        }
        if (symbolId != null) {
            sql.append(" AND symbol_id = :symbolId");
            mapSqlParameterSource.addValue("symbolId", symbolId);
        }
        if (id != null) {
            sql.append(" AND order_id = :id");
            mapSqlParameterSource.addValue("id", id);
        }
        try {
            return historicalTransactionManager.findSingleFromHistoryWithMultipleConditions(
                    sql.toString(), mapSqlParameterSource, newRowMapper());
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    public PosTrade findByPosTrade(Long userId, Long symbolId, Long orderId) {
        if (symbolId == null && orderId == null) {
            return new PosTrade();
        }
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosTrade, PosTrade>() {
                    @Override
                    public PosTrade query() {
                        List<Predicate> predicates = new ArrayList<>();
                        if (orderId != null) {
                            predicates.add(predicate.equalOrderId(criteriaBuilder, root, orderId));
                        }
                        if (symbolId != null) {
                            predicates.add(
                                    predicate.equalSymbolId(criteriaBuilder, root, symbolId));
                        }
                        if (userId != null) {
                            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        }
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public Long countById(Long id, UserIdType userIdType, Long userId) {

        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        Stream.of(
                                                        criteriaBuilder.equal(
                                                                root.get(PosTrade_.id), id),
                                                        criteriaBuilder.equal(
                                                                root.get(PosTrade_.orderSide),
                                                                OrderSide.SELL),
                                                        criteriaBuilder.equal(
                                                                root.get(PosTrade_.idType),
                                                                userIdType),
                                                        criteriaBuilder.equal(
                                                                root.get(PosTrade_.userId), userId))
                                                .collect(Collectors.toList()));
                            }
                        });
        StringBuilder sql =
                new StringBuilder(
                        "select * from pos_trade where id = :id and id_type = :id_type and user_id = :user_id");
        // 条件指定
        try {
            MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
            mapSqlParameterSource.addValue("id", id);
            mapSqlParameterSource.addValue("id_type", userIdType);
            mapSqlParameterSource.addValue("user_id", userId);
            List<PosTrade> histDataList =
                    historicalTransactionManager.findFromHistory(
                            sql.toString(), mapSqlParameterSource, newRowMapper());
            return histDataList.size() + count;
        } catch (EmptyResultDataAccessException e) {
            return count;
        }
    }

    public List<PosTrade> findByCondition(
            Long symbolId,
            Long userId,
            Date createdAtFrom,
            Date createdAtTo,
            EntityManager entityManager) {
        return new QueryExecutorReturner<PosTrade, List<PosTrade>>() {
            @Override
            public List<PosTrade> query() {

                List<Predicate> predicates =
                        createPredicatesOfFindByCondition(
                                criteriaBuilder,
                                root,
                                symbolId,
                                userId,
                                null,
                                null,
                                null,
                                null,
                                null,
                                createdAtFrom != null ? createdAtFrom.getTime() : null,
                                createdAtTo != null ? createdAtTo.getTime() : null,
                                null,
                                null,
                                null,
                                null,
                                null,
                                null,
                                null,
                                null);

                return getResultList(
                        entityManager,
                        criteriaQuery,
                        root,
                        predicates,
                        criteriaBuilder.asc(root.get(Trade_.id)));
            }
        }.execute(getEntityClass(), entityManager);
    }

    private List<Predicate> createPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<PosTrade> root,
            Long symbolId,
            Long userId,
            List<Long> userIds,
            List<Long> exceptUserIds,
            Long id,
            Long idFrom,
            Long idTo,
            Long createdAtFrom,
            Long createdAtTo,
            TradeAction tradeAction,
            OrderSide orderSide,
            OrderType orderType,
            OrderType[] orderTypes,
            OrderType[] exceptOrderTypes,
            OrderChannel orderChannel,
            Long orderId,
            Long targetOrderId) {
        List<Predicate> predicates =
                createPredicates(criteriaBuilder, root, symbolId, userId, userIds, exceptUserIds);

        if (id != null) {
            predicates.add(predicate.equalId(criteriaBuilder, root, id));
        } else {
            if (idFrom != null) {
                predicates.add(predicate.greaterThanOrEqualToId(criteriaBuilder, root, idFrom));
            }
            if (idTo != null) {
                predicates.add(predicate.lessThanOrEqualToId(criteriaBuilder, root, idTo));
            }
            if (createdAtFrom != null) {
                predicates.add(
                        predicate.greaterThanOrEqualToCreatedAt(
                                criteriaBuilder, root, new Date(createdAtFrom)));
            }
            if (createdAtTo != null) {
                predicates.add(
                        predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(createdAtTo)));
            }

            if (tradeAction != null) {
                predicates.add(predicate.equalTradeAction(criteriaBuilder, root, tradeAction));
            }

            if (orderSide != null) {
                predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
            }
            if (orderType != null) {
                predicates.add(predicate.equalOrderType(criteriaBuilder, root, orderType));
            } else if (!ArrayUtils.isEmpty(orderTypes)) {
                predicates.add(predicate.inOrderType(root, orderTypes));
            } else if (!ArrayUtils.isEmpty(exceptOrderTypes)) {
                predicates.add(predicate.notInOrderType(criteriaBuilder, root, exceptOrderTypes));
            }
            if (orderChannel != null) {
                predicates.add(predicate.equalOrderChannel(criteriaBuilder, root, orderChannel));
            }
            if (orderId != null) {
                predicates.add(predicate.equalOrderId(criteriaBuilder, root, orderId));
            }
            if (targetOrderId != null) {
                predicates.add(predicate.equalTargetOrderId(criteriaBuilder, root, targetOrderId));
            }
        }
        return predicates;
    }

    // インデックス: symbol_id, user_id
    protected List<Predicate> createPredicates(
            CriteriaBuilder criteriaBuilder,
            Root<PosTrade> root,
            Long symbolId,
            Long userId,
            List<Long> userIds,
            List<Long> exceptUserIds) {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));

        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        } else {
            if (!CollectionUtils.isEmpty(userIds)) {
                predicates.add(predicate.inUserIds(criteriaBuilder, root, userIds));
            }
            if (!CollectionUtils.isEmpty(exceptUserIds)) {
                predicates.add(predicate.inExceptUserIds(criteriaBuilder, root, exceptUserIds));
            }
        }
        return predicates;
    }

    public List<PosTrade> findMakerByCondition(Long symbolId, Date from, Date to) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosTrade, List<PosTrade>>() {
                    @Override
                    public List<PosTrade> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        // POUH-19 provide data from SELL orders only
                        predicates.add(
                                predicate.equalOrderSide(criteriaBuilder, root, OrderSide.SELL));
                        predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
                        predicates.add(
                                predicate.equalTradeAction(
                                        criteriaBuilder, root, TradeAction.TAKER));
                        predicates.add(
                                predicate.greaterThanOrEqualToCreatedAt(
                                        criteriaBuilder, root, from));
                        predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, to));
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.asc(root.get(Trade_.createdAt)),
                                criteriaBuilder.asc(root.get(Trade_.id)));
                    }
                });
    }

    public PageData<PosTrade> findByConditionPageData(
            Long symbolId,
            Long userId,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo,
            TradeAction tradeAction,
            OrderSide orderSide,
            OrderType orderType,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            Integer number,
            Integer size,
            boolean isAscending) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        createPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                symbolId,
                                                userId,
                                                null,
                                                null,
                                                id,
                                                idFrom,
                                                idTo,
                                                dateFrom,
                                                dateTo,
                                                tradeAction,
                                                orderSide,
                                                orderType,
                                                CollectionUtils.isEmpty(orderTypes)
                                                        ? null
                                                        : orderTypes.toArray(
                                                                new OrderType[orderTypes.size()]),
                                                CollectionUtils.isEmpty(exceptOrderTypes)
                                                        ? null
                                                        : exceptOrderTypes.toArray(
                                                                new OrderType
                                                                        [exceptOrderTypes.size()]),
                                                null,
                                                null,
                                                null));
                            }
                        });
        return new PageData<PosTrade>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<PosTrade, List<PosTrade>>() {
                            @Override
                            public List<PosTrade> query() {

                                List<Predicate> predicates =
                                        createPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                symbolId,
                                                userId,
                                                null,
                                                null,
                                                id,
                                                idFrom,
                                                idTo,
                                                dateFrom,
                                                dateTo,
                                                tradeAction,
                                                orderSide,
                                                orderType,
                                                CollectionUtils.isEmpty(orderTypes)
                                                        ? null
                                                        : orderTypes.toArray(
                                                                new OrderType[orderTypes.size()]),
                                                CollectionUtils.isEmpty(exceptOrderTypes)
                                                        ? null
                                                        : exceptOrderTypes.toArray(
                                                                new OrderType
                                                                        [exceptOrderTypes.size()]),
                                                null,
                                                null,
                                                null);

                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        isAscending
                                                ? criteriaBuilder.asc(root.get(Trade_.id))
                                                : criteriaBuilder.desc(root.get(Trade_.id)));
                            }
                        }));
    }

    public List<PosTrade> findByCondition(
            Long symbolId,
            Long userId,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo,
            TradeAction tradeAction,
            OrderSide orderSide,
            OrderType orderType,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            Integer number,
            Integer size,
            boolean isAscending) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosTrade, List<PosTrade>>() {
                    @Override
                    public List<PosTrade> query() {

                        List<Predicate> predicates =
                                createPredicatesOfFindByCondition(
                                        criteriaBuilder,
                                        root,
                                        symbolId,
                                        userId,
                                        null,
                                        null,
                                        id,
                                        idFrom,
                                        idTo,
                                        dateFrom,
                                        dateTo,
                                        tradeAction,
                                        orderSide,
                                        orderType,
                                        CollectionUtils.isEmpty(orderTypes)
                                                ? null
                                                : orderTypes.toArray(
                                                        new OrderType[orderTypes.size()]),
                                        CollectionUtils.isEmpty(exceptOrderTypes)
                                                ? null
                                                : exceptOrderTypes.toArray(
                                                        new OrderType[exceptOrderTypes.size()]),
                                        null,
                                        null,
                                        null);

                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                number,
                                size,
                                isAscending
                                        ? criteriaBuilder.asc(root.get(Trade_.id))
                                        : criteriaBuilder.desc(root.get(Trade_.id)));
                    }
                });
    }

    public List<PosTrade> findAllByCondition(
            Long symbolId,
            Long userId,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo,
            TradeAction tradeAction,
            OrderSide orderSide,
            OrderType orderType) {
        return new ArrayList<PosTrade>(
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<PosTrade, List<PosTrade>>() {
                            @Override
                            public List<PosTrade> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                symbolId,
                                                userId,
                                                id,
                                                idFrom,
                                                idTo,
                                                dateFrom,
                                                dateTo,
                                                tradeAction,
                                                orderSide,
                                                orderType,
                                                null,
                                                null);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.desc(root.get(Trade_.id)));
                            }
                        }));
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<PosTrade> root,
            Long symbolId,
            Long userId,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo,
            TradeAction tradeAction,
            OrderSide orderSide,
            OrderType orderType,
            Integer number,
            Integer size) {
        List<Predicate> predicates = new ArrayList<>();

        if (symbolId != null) {
            predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
        }

        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        }

        if (id != null) {
            predicates.add(predicate.equalId(criteriaBuilder, root, id));
        } else {
            if (idFrom != null) {
                predicates.add(predicate.greaterThanOrEqualToId(criteriaBuilder, root, idFrom));
            }

            if (idTo != null) {
                predicates.add(predicate.lessThanId(criteriaBuilder, root, idTo));
            }

            if (dateFrom != null) {
                predicates.add(
                        predicate.greaterThanOrEqualToCreatedAt(
                                criteriaBuilder, root, new Date(dateFrom)));
            }

            if (dateTo != null) {
                predicates.add(
                        predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
            }

            if (tradeAction != null) {
                predicates.add(predicate.equalTradeAction(criteriaBuilder, root, tradeAction));
            }

            if (orderSide != null) {
                predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
            }

            if (orderType != null) {
                predicates.add(predicate.equalOrderType(criteriaBuilder, root, orderType));
            }
        }
        return predicates;
    }

    // 販売所集計
    private StringBuilder createSumSqlString(
            MapSqlParameterSource mapSqlParameterSource,
            Symbol symbol,
            Currency quoteCurrency,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            Date createdAtFrom,
            Date createdAtTo) {

        StringBuilder sql = new StringBuilder("select");

        // SELECT項目・集計関数
        if (quoteCurrency == null) {
            // BASE集計
            sql.append(" sum(case when order_side = 'BUY' then amount else 0 end) as buyamount,");
            sql.append(
                    " sum(case when order_side = 'BUY' then asset_amount::decimal(34,10) * jpy_conversion::decimal(34,10) else 0 end)::decimal(34,20) as buyamountjpy,");
            sql.append(" sum(case when order_side = 'SELL' then amount else 0 end) as sellamount,");
            sql.append(
                    " sum(case when order_side = 'SELL' then asset_amount::decimal(34,10) * jpy_conversion::decimal(34,10) else 0 end)::decimal(34,20) as sellamountjpy");
        } else {
            // QUOTE集計
            // BTCJPY買い(1BTC500万)の場合、JPYは500万(assetAmount)売り
            sql.append(
                    " sum(case when order_side = 'SELL' then asset_amount else 0 end) as buyamount,");
            sql.append(
                    " sum(case when order_side = 'SELL' then asset_amount::decimal(34,10) * jpy_conversion::decimal(34,10) else 0 end)::decimal(34,20) as buyamountjpy,");
            sql.append(
                    " sum(case when order_side = 'BUY' then asset_amount else 0 end) as sellamount,");
            sql.append(
                    " sum(case when order_side = 'BUY' then asset_amount::decimal(34,10) * jpy_conversion::decimal(34,10) else 0 end)::decimal(34,20) as sellamountjpy,");
            sql.append(" sum(fee) as feesum,");
            sql.append(
                    " sum(fee::decimal(34,10) * jpy_conversion::decimal(34,10))::decimal(34,20) as feesumjpy,");
            sql.append(
                    " sum(case when order_side = 'BUY' then asset_amount else -1 * asset_amount end) as profit,");
            sql.append(
                    " sum(case when order_side = 'BUY' then asset_amount::decimal(34,10) * jpy_conversion::decimal(34,10) else -1 * asset_amount::decimal(34,10) * jpy_conversion::decimal(34,10) end)::decimal(34,20) as profitjpy");
        }

        // テーブル
        sql.append(" from pos_trade ");

        // 条件指定
        sql.append(" where symbol_id = :symbol_id");
        mapSqlParameterSource.addValue("symbol_id", symbol.getId());

        if (!CollectionUtils.isEmpty(orderTypes)) {
            sql.append(" and order_type in (:order_types)");
            mapSqlParameterSource.addValue(
                    "order_types",
                    orderTypes.stream().map((type) -> type.name()).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(exceptOrderTypes)) {
            sql.append(" and order_type not in (:except_order_types)");
            mapSqlParameterSource.addValue(
                    "except_order_types",
                    exceptOrderTypes.stream()
                            .map((type) -> type.name())
                            .collect(Collectors.toList()));
        }

        if (createdAtFrom != null) {
            sql.append(" and created_at >= :created_at_from");
            mapSqlParameterSource.addValue("created_at_from", createdAtFrom);
        }

        if (createdAtTo != null) {
            sql.append(" and created_at < :created_at_to");
            mapSqlParameterSource.addValue("created_at_to", createdAtTo);
        }

        return sql;
    }

    // 販売所集計(アーカイブから)
    public Map<String, Object> sumPosExchangeByConditionFromHistory(
            Symbol symbol,
            Currency quoteCurrency,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            Date createdAtFrom,
            Date createdAtTo) {
        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();

        StringBuilder sql =
                createSumSqlString(
                        mapSqlParameterSource,
                        symbol,
                        quoteCurrency,
                        orderTypes,
                        exceptOrderTypes,
                        createdAtFrom,
                        createdAtTo);

        log.info("DBMITO,ExchangeSummaryLog,redshift-sql:" + sql.toString());

        return historicalTransactionManager.queryForMapFromHistory(
                sql.toString(), mapSqlParameterSource);
    }

    // 販売所別サマリ
    public Object[] sumPosExchangeByCondition(
            Long symbolId,
            Currency quoteCurrency,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            Date createdAtFrom,
            Date createdAtTo) {
        return customTransactionManager.sum(
                getEntityClass(),
                new QueryExecutorSum<PosTrade>() {
                    @Override
                    public Object[] query() {

                        List<Predicate> predicates =
                                createPredicatesOfFindByCondition(
                                        criteriaBuilder,
                                        root,
                                        symbolId,
                                        null,
                                        null,
                                        null,
                                        null,
                                        null,
                                        null,
                                        createdAtFrom != null ? createdAtFrom.getTime() : null,
                                        createdAtTo != null ? createdAtTo.getTime() : null,
                                        null,
                                        null,
                                        null,
                                        CollectionUtils.isEmpty(orderTypes)
                                                ? null
                                                : orderTypes.toArray(
                                                        new OrderType[orderTypes.size()]),
                                        CollectionUtils.isEmpty(exceptOrderTypes)
                                                ? null
                                                : exceptOrderTypes.toArray(
                                                        new OrderType[exceptOrderTypes.size()]),
                                        null,
                                        null,
                                        null);

                        if (quoteCurrency == null) {
                            return sumExchangeBase(
                                    entityManager,
                                    criteriaBuilder,
                                    criteriaQuery,
                                    root,
                                    predicates);
                        } else {
                            return sumExchangeQuote(
                                    entityManager,
                                    criteriaBuilder,
                                    criteriaQuery,
                                    root,
                                    predicates);
                        }
                    }
                });
    }

    // 0-2-1
    private Object[] sumExchangeBase(
            EntityManager entityManager,
            CriteriaBuilder criteriaBuilder,
            CriteriaQuery<Object[]> criteriaQuery,
            Root<PosTrade> root,
            List<Predicate> predicates) {

        criteriaQuery.multiselect(
                // 買い約定数量
                criteriaBuilder.sum(
                        criteriaBuilder
                                .<BigDecimal>selectCase()
                                .when(
                                        criteriaBuilder.equal(
                                                root.get(PosTrade_.orderSide), OrderSide.BUY),
                                        root.get(Trade_.amount))
                                .otherwise(BigDecimal.ZERO)),
                // 買い約定数量（円貨換算）
                criteriaBuilder.sum(
                        criteriaBuilder
                                .<BigDecimal>selectCase()
                                .when(
                                        criteriaBuilder.equal(
                                                root.get(PosTrade_.orderSide), OrderSide.BUY),
                                        criteriaBuilder.prod(
                                                root.get(PosTrade_.assetAmount),
                                                root.get(PosTrade_.jpyConversion)))
                                .otherwise(BigDecimal.ZERO)),
                // 売り約定数量
                criteriaBuilder.sum(
                        criteriaBuilder
                                .<BigDecimal>selectCase()
                                .when(
                                        criteriaBuilder.equal(
                                                root.get(PosTrade_.orderSide), OrderSide.SELL),
                                        root.get(Trade_.amount))
                                .otherwise(BigDecimal.ZERO)),
                // 売り約定数量（円貨換算）
                criteriaBuilder.sum(
                        criteriaBuilder
                                .<BigDecimal>selectCase()
                                .when(
                                        criteriaBuilder.equal(
                                                root.get(PosTrade_.orderSide), OrderSide.SELL),
                                        criteriaBuilder.prod(
                                                root.get(PosTrade_.assetAmount),
                                                root.get(PosTrade_.jpyConversion)))
                                .otherwise(BigDecimal.ZERO)));

        if (!CollectionUtils.isEmpty(predicates)) {
            criteriaQuery.where(predicates.toArray(new Predicate[] {}));
        }

        try {
            return entityManager
                    .createQuery(criteriaQuery)
                    .setFirstResult(0)
                    .setMaxResults(1)
                    .getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }

    // 0-2-2
    private Object[] sumExchangeQuote(
            EntityManager entityManager,
            CriteriaBuilder criteriaBuilder,
            CriteriaQuery<Object[]> criteriaQuery,
            Root<PosTrade> root,
            List<Predicate> predicates) {

        // BTCJPY買い(1BTC500万)の場合、JPYは500万(assetAmount)売り
        criteriaQuery.multiselect(
                // [0]買い約定数量
                criteriaBuilder.sum(
                        criteriaBuilder
                                .<BigDecimal>selectCase()
                                .when(
                                        criteriaBuilder.equal(
                                                root.get(PosTrade_.orderSide), OrderSide.SELL),
                                        root.get(PosTrade_.assetAmount))
                                .otherwise(BigDecimal.ZERO)),
                // [1]買い約定数量（円貨換算）
                criteriaBuilder.sum(
                        criteriaBuilder
                                .<BigDecimal>selectCase()
                                .when(
                                        criteriaBuilder.equal(
                                                root.get(PosTrade_.orderSide), OrderSide.SELL),
                                        criteriaBuilder.prod(
                                                root.get(PosTrade_.assetAmount),
                                                root.get(PosTrade_.jpyConversion)))
                                .otherwise(BigDecimal.ZERO)),
                // [2]売り約定数量
                criteriaBuilder.sum(
                        criteriaBuilder
                                .<BigDecimal>selectCase()
                                .when(
                                        criteriaBuilder.equal(
                                                root.get(PosTrade_.orderSide), OrderSide.BUY),
                                        root.get(PosTrade_.assetAmount))
                                .otherwise(BigDecimal.ZERO)),
                // [3]売り約定数量（円貨換算）
                criteriaBuilder.sum(
                        criteriaBuilder
                                .<BigDecimal>selectCase()
                                .when(
                                        criteriaBuilder.equal(
                                                root.get(PosTrade_.orderSide), OrderSide.BUY),
                                        criteriaBuilder.prod(
                                                root.get(PosTrade_.assetAmount),
                                                root.get(PosTrade_.jpyConversion)))
                                .otherwise(BigDecimal.ZERO)),
                // [4]約定手数料
                criteriaBuilder.sum(root.get(Trade_.fee)),
                // [5]約定手数料（円貨換算）
                criteriaBuilder.sum(
                        criteriaBuilder.prod(
                                root.get(Trade_.fee), root.get(PosTrade_.jpyConversion))),
                // [6]販売所損益
                //  BTCJPY 買いはユーザーのquote assetAmountマイナス、事業者プラス
                //  => 買いassetAmount - 売りassetAmount
                criteriaBuilder.sum(
                        criteriaBuilder
                                .<BigDecimal>selectCase()
                                .when(
                                        criteriaBuilder.equal(
                                                root.get(PosTrade_.orderSide), OrderSide.BUY),
                                        root.get(PosTrade_.assetAmount))
                                .otherwise(criteriaBuilder.neg(root.get(PosTrade_.assetAmount)))),
                // [7]販売所損益（円貨換算）
                criteriaBuilder.sum(
                        criteriaBuilder
                                .<BigDecimal>selectCase()
                                .when(
                                        criteriaBuilder.equal(
                                                root.get(PosTrade_.orderSide), OrderSide.BUY),
                                        criteriaBuilder.prod(
                                                root.get(PosTrade_.assetAmount),
                                                root.get(PosTrade_.jpyConversion)))
                                .otherwise(
                                        criteriaBuilder.prod(
                                                criteriaBuilder.neg(
                                                        root.get(PosTrade_.assetAmount)),
                                                root.get(PosTrade_.jpyConversion)))));

        if (!CollectionUtils.isEmpty(predicates)) {
            criteriaQuery.where(predicates.toArray(new Predicate[] {}));
        }

        try {
            return entityManager
                    .createQuery(criteriaQuery)
                    .setFirstResult(0)
                    .setMaxResults(1)
                    .getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }

    public List<PosTrade> findAllFromHistory(
            Symbol symbol,
            Long userId,
            Long id,
            Long idFrom,
            Long idTo,
            OrderSide orderSide,
            OrderType orderType,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            OrderChannel orderChannel,
            BigDecimal price,
            BigDecimal amount,
            BigDecimal jpyConversion,
            TradeAction tradeAction,
            Long orderId,
            BigDecimal fee,
            Date createdAtFrom,
            Date createdAtTo) {

        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();

        StringBuilder sql =
                createSqlString(
                        mapSqlParameterSource,
                        symbol,
                        userId,
                        null,
                        null,
                        id,
                        idFrom,
                        idTo,
                        orderSide,
                        orderType,
                        orderTypes,
                        exceptOrderTypes,
                        orderChannel,
                        price,
                        amount,
                        jpyConversion,
                        tradeAction,
                        orderId,
                        fee,
                        createdAtFrom,
                        createdAtTo,
                        null,
                        null);

        log.info("redshift-sql:" + sql.toString());
        log.info("redshift-params:" + mapSqlParameterSource.getValues().toString());

        return historicalTransactionManager.findFromHistory(
                sql.toString(), mapSqlParameterSource, newRowMapper());
    }

    private StringBuilder createSqlString(
            MapSqlParameterSource mapSqlParameterSource,
            Symbol symbol,
            Long userId,
            List<Long> userIds,
            List<Long> exceptUserIds,
            Long id,
            Long idFrom,
            Long idTo,
            OrderSide orderSide,
            OrderType orderType,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            OrderChannel orderChannel,
            BigDecimal price,
            BigDecimal amount,
            BigDecimal jpyConversion,
            TradeAction tradeAction,
            Long orderId,
            BigDecimal fee,
            Date createdAtFrom,
            Date createdAtTo,
            Integer number,
            Integer size) {

        StringBuilder sql = new StringBuilder("select * from pos_trade");

        // 条件指定
        sql.append(" where symbol_id = :symbol_id");
        mapSqlParameterSource.addValue("symbol_id", symbol.getId());

        if (id != null) {
            sql.append(" and id = :id");
            mapSqlParameterSource.addValue("id", id);
        } else {
            if (userId != null) {
                sql.append(" and user_id = :user_id");
                mapSqlParameterSource.addValue("user_id", userId);
            } else {
                if (userIds != null) {
                    sql.append(" and user_id in (:user_ids)");
                    mapSqlParameterSource.addValue("user_ids", userIds);
                }

                if (exceptUserIds != null) {
                    sql.append(" and user_id not in (:except_user_ids)");
                    mapSqlParameterSource.addValue("except_user_ids", exceptUserIds);
                }
            }

            if (idFrom != null) {
                sql.append(" and id >= :id_from");
                mapSqlParameterSource.addValue("id_from", idFrom);
            }

            if (idTo != null) {
                sql.append(" and id <= :id_to");
                mapSqlParameterSource.addValue("id_to", idTo);
            }
            if (orderSide != null) {
                sql.append(" and order_side = :order_side");
                mapSqlParameterSource.addValue("order_side", orderSide.toString());
            }
            if (orderType != null) {
                sql.append(" and order_type = :order_type");
                mapSqlParameterSource.addValue("order_type", orderType.toString());
            } else {
                if (!CollectionUtils.isEmpty(orderTypes)) {
                    sql.append(" and order_type in (:order_types)");
                    mapSqlParameterSource.addValue(
                            "order_types",
                            orderTypes.stream()
                                    .map((type) -> type.name())
                                    .collect(Collectors.toList()));
                }
                if (!CollectionUtils.isEmpty(exceptOrderTypes)) {
                    sql.append(" and order_type not in (:except_order_types)");
                    mapSqlParameterSource.addValue(
                            "except_order_types",
                            exceptOrderTypes.stream()
                                    .map((type) -> type.name())
                                    .collect(Collectors.toList()));
                }
            }
            if (orderChannel != null) {
                sql.append(" and order_channel = :order_channel");
                mapSqlParameterSource.addValue("order_channel", orderChannel.toString());
            }

            if (price != null) {
                sql.append(" and price = :price");
                mapSqlParameterSource.addValue("price", price.toPlainString());
            }

            if (amount != null) {
                sql.append(" and amount = :amount");
                mapSqlParameterSource.addValue("amount", amount.toPlainString());
            }

            if (jpyConversion != null) {
                sql.append(" and jpy_conversion = :jpy_conversion");
                mapSqlParameterSource.addValue("jpy_conversion", jpyConversion.toPlainString());
            }

            if (tradeAction != null) {
                sql.append(" and trade_action = :trade_action");
                mapSqlParameterSource.addValue("trade_action", tradeAction.toString());
            }

            if (orderId != null) {
                sql.append(" and order_id = :order_id");
                mapSqlParameterSource.addValue("order_id", orderId);
            }

            if (fee != null) {
                sql.append(" and fee = :fee");
                mapSqlParameterSource.addValue("fee", fee.toPlainString());
            }

            if (createdAtFrom != null) {
                sql.append(" and created_at >= :created_at_from");
                mapSqlParameterSource.addValue("created_at_from", createdAtFrom);
            }

            if (createdAtTo != null) {
                sql.append(" and created_at < :created_at_to");
                mapSqlParameterSource.addValue("created_at_to", createdAtTo);
            }
            // ページング指定
            if (size != null && size != 0) {
                sql.append(" limit " + size.toString());
            }
            if (number != null && number != 0) {
                sql.append(" offset " + number.toString());
            }
        }

        return sql;
    }

    // 不適正取引検知集計
    public List<Object[]> sumOverTradesByCondition(
            Long symbolId,
            BigDecimal tradeAssetAmount,
            List<Long> exceptUserIds,
            Long dateFrom,
            Long dateTo) {
        return customTransactionManager.sumList(
                getEntityClass(),
                new QueryExecutorSumList<PosTrade>() {
                    @Override
                    public List<Object[]> query() {

                        List<Predicate> predicates =
                                createPredicatesOfFindByCondition(
                                        criteriaBuilder,
                                        root,
                                        symbolId,
                                        null,
                                        null,
                                        exceptUserIds,
                                        null,
                                        null,
                                        null,
                                        dateFrom,
                                        dateTo,
                                        null,
                                        null,
                                        null,
                                        null,
                                        null,
                                        null,
                                        null,
                                        null);

                        return sumOverTrades(
                                entityManager,
                                criteriaBuilder,
                                criteriaQuery,
                                root,
                                predicates,
                                tradeAssetAmount);
                    }
                });
    }

    // 不適正取引検知集計クエリ
    private List<Object[]> sumOverTrades(
            EntityManager entityManager,
            CriteriaBuilder criteriaBuilder,
            CriteriaQuery<Object[]> criteriaQuery,
            Root<PosTrade> root,
            List<Predicate> predicates,
            BigDecimal tradeAssetAmount) {

        // 一定の期間で、約定総額(円貨換算)が{TRADE_ASSET_AMOUNT}以上の
        // 約定件数のユーザーID別リスト取得

        /** group by */
        criteriaQuery.groupBy(root.get(Trade_.userId));

        /** select */
        criteriaQuery.multiselect(
                // ユーザーID
                root.get(Trade_.userId),
                // 約定総額（円貨換算）
                //   例)ETHBTCの場合、assetAmountはBase単位(BTC), jpyConversionはquoteJPY(BTCJPY)
                criteriaBuilder.sum(
                        criteriaBuilder
                                .<Integer>selectCase()
                                .when(
                                        // 閾値「以上」
                                        criteriaBuilder.greaterThanOrEqualTo(
                                                criteriaBuilder.prod(
                                                        root.get(PosTrade_.assetAmount),
                                                        root.get(PosTrade_.jpyConversion)),
                                                tradeAssetAmount),
                                        1)
                                .otherwise(0)));

        /** where */
        if (!CollectionUtils.isEmpty(predicates)) {
            criteriaQuery.where(predicates.toArray(new Predicate[] {}));
        }

        /** result */
        try {
            return entityManager.createQuery(criteriaQuery).getResultList();
        } catch (Exception e) {
            return null;
        }
    }

    // 販売所約定履歴を取得
    public List<PosTrade> findByCondition(
            Long symbolId, Long userId, Date createdAtFrom, Date createdAtTo) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosTrade, List<PosTrade>>() {
                    @Override
                    public List<PosTrade> query() {
                        {
                            List<Predicate> predicates =
                                    createPredicatesOfFindByCondition(
                                            criteriaBuilder,
                                            root,
                                            symbolId,
                                            userId,
                                            null,
                                            null,
                                            null,
                                            null,
                                            null,
                                            createdAtFrom != null ? createdAtFrom.getTime() : null,
                                            createdAtTo != null ? createdAtTo.getTime() : null,
                                            null,
                                            null,
                                            null,
                                            null,
                                            null,
                                            null,
                                            null,
                                            null);

                            return getResultList(
                                    entityManager,
                                    criteriaQuery,
                                    root,
                                    predicates,
                                    criteriaBuilder.asc(root.get(Trade_.id)));
                        }
                    }
                });
    }

    // 取引報告書CSV出力（POS_TRADE）
    public List<PosTrade> findFromPosTradeHistory(
            Symbol symbol,
            Long userId,
            OrderSide orderSide,
            OrderType orderType,
            OrderChannel orderChannel,
            Date createdAtFrom,
            Date createdAtTo,
            Integer number,
            Integer size) {
        return findFromPosTradeHistory(
                symbol,
                userId,
                orderSide,
                orderType,
                orderChannel,
                null,
                null,
                null,
                null,
                null,
                null,
                createdAtFrom,
                createdAtTo,
                number,
                size);
    }

    // 販売所約定履歴Historyを取得
    public List<PosTrade> findFromPosTradeHistory(
            Symbol symbol,
            Long userId,
            OrderSide orderSide,
            OrderType orderType,
            OrderChannel orderChannel,
            BigDecimal price,
            BigDecimal amount,
            BigDecimal jpyConversion,
            TradeAction tradeAction,
            Long orderId,
            BigDecimal fee,
            Date createdAtFrom,
            Date createdAtTo,
            Integer number,
            Integer size) {

        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();

        StringBuilder sql =
                createSqlStringPosTrade(
                        mapSqlParameterSource,
                        symbol,
                        userId,
                        null,
                        null,
                        null,
                        null,
                        null,
                        orderSide,
                        orderType,
                        null,
                        null,
                        orderChannel,
                        price,
                        amount,
                        jpyConversion,
                        tradeAction,
                        orderId,
                        fee,
                        createdAtFrom,
                        createdAtTo,
                        number,
                        size);

        log.info("redshift-sql:" + sql.toString());
        log.info("redshift-params:" + mapSqlParameterSource.getValues().toString());

        return historicalTransactionManager.findFromHistory(
                sql.toString(), mapSqlParameterSource, newRowMapper());
    }

    // 販売所約定履歴HistoryのSql作成
    private StringBuilder createSqlStringPosTrade(
            MapSqlParameterSource mapSqlParameterSource,
            Symbol symbol,
            Long userId,
            List<Long> userIds,
            List<Long> exceptUserIds,
            Long id,
            Long idFrom,
            Long idTo,
            OrderSide orderSide,
            OrderType orderType,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            OrderChannel orderChannel,
            BigDecimal price,
            BigDecimal amount,
            BigDecimal jpyConversion,
            TradeAction tradeAction,
            Long orderId,
            BigDecimal fee,
            Date createdAtFrom,
            Date createdAtTo,
            Integer number,
            Integer size) {

        StringBuilder sql = new StringBuilder("select * from pos_trade");

        // 条件指定
        sql.append(" where symbol_id = :symbol_id");
        mapSqlParameterSource.addValue("symbol_id", symbol.getId());

        if (id != null) {
            sql.append(" and id = :id");
            mapSqlParameterSource.addValue("id", id);
        } else {
            if (userId != null) {
                sql.append(" and user_id = :user_id");
                mapSqlParameterSource.addValue("user_id", userId);
            } else {
                if (userIds != null) {
                    sql.append(" and user_id in (:user_ids)");
                    mapSqlParameterSource.addValue("user_ids", userIds);
                }

                if (exceptUserIds != null) {
                    sql.append(" and user_id not in (:except_user_ids)");
                    mapSqlParameterSource.addValue("except_user_ids", exceptUserIds);
                }
            }

            if (idFrom != null) {
                sql.append(" and id >= :id_from");
                mapSqlParameterSource.addValue("id_from", idFrom);
            }

            if (idTo != null) {
                sql.append(" and id <= :id_to");
                mapSqlParameterSource.addValue("id_to", idTo);
            }
            if (orderSide != null) {
                sql.append(" and order_side = :order_side");
                mapSqlParameterSource.addValue("order_side", orderSide.toString());
            }
            if (orderType != null) {
                sql.append(" and order_type = :order_type");
                mapSqlParameterSource.addValue("order_type", orderType.toString());
            } else {
                if (!CollectionUtils.isEmpty(orderTypes)) {
                    sql.append(" and order_type in (:order_types)");
                    mapSqlParameterSource.addValue(
                            "order_types",
                            orderTypes.stream()
                                    .map((type) -> type.name())
                                    .collect(Collectors.toList()));
                }
                if (!CollectionUtils.isEmpty(exceptOrderTypes)) {
                    sql.append(" and order_type not in (:except_order_types)");
                    mapSqlParameterSource.addValue(
                            "except_order_types",
                            exceptOrderTypes.stream()
                                    .map((type) -> type.name())
                                    .collect(Collectors.toList()));
                }
            }
            if (orderChannel != null) {
                sql.append(" and order_channel = :order_channel");
                mapSqlParameterSource.addValue("order_channel", orderChannel.toString());
            }

            if (price != null) {
                sql.append(" and price = :price");
                mapSqlParameterSource.addValue("price", price.toPlainString());
            }

            if (amount != null) {
                sql.append(" and amount = :amount");
                mapSqlParameterSource.addValue("amount", amount.toPlainString());
            }

            if (jpyConversion != null) {
                sql.append(" and jpy_conversion = :jpy_conversion");
                mapSqlParameterSource.addValue("jpy_conversion", jpyConversion.toPlainString());
            }

            if (tradeAction != null) {
                sql.append(" and trade_action = :trade_action");
                mapSqlParameterSource.addValue("trade_action", tradeAction.toString());
            }

            if (orderId != null) {
                sql.append(" and order_id = :order_id");
                mapSqlParameterSource.addValue("order_id", orderId);
            }

            if (fee != null) {
                sql.append(" and fee = :fee");
                mapSqlParameterSource.addValue("fee", fee.toPlainString());
            }

            if (createdAtFrom != null) {
                sql.append(" and created_at >= :created_at_from");
                mapSqlParameterSource.addValue("created_at_from", createdAtFrom);
            }

            if (createdAtTo != null) {
                sql.append(" and created_at < :created_at_to");
                mapSqlParameterSource.addValue("created_at_to", createdAtTo);
            }
            // ページング指定
            if (size != null && size != 0) {
                sql.append(" limit " + size.toString());
            }
            if (number != null && number != 0) {
                sql.append(" offset " + number.toString());
            }
        }

        return sql;
    }

    // ユーザー別約定件数カウント
    public List<Object[]> countPosTradesByUserByCondition(
            Long symbolId, List<Long> exceptUserIds, Long dateFrom, Long dateTo) {
        return customTransactionManager.sumList(
                getEntityClass(),
                new QueryExecutorSumList<PosTrade>() {
                    @Override
                    public List<Object[]> query() {

                        List<Predicate> predicates =
                                createPredicatesOfFindByCondition(
                                        criteriaBuilder,
                                        root,
                                        symbolId,
                                        null,
                                        null,
                                        exceptUserIds,
                                        null,
                                        null,
                                        null,
                                        dateFrom,
                                        dateTo,
                                        null,
                                        null,
                                        null,
                                        null,
                                        null,
                                        null,
                                        null,
                                        null);

                        return countPosTradesByUserByConditionQuery(
                                entityManager, criteriaBuilder, criteriaQuery, root, predicates);
                    }
                });
    }

    // BO管理画面専用、orderChannel検索条件が必要、さらに、ユーザーIDが複数入力可能です。
    public List<PosTrade> findByConditionForBo(
            Long symbolId,
            List<Long> userIds,
            List<Long> exceptUserIds,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo,
            TradeAction tradeAction,
            OrderType orderType,
            OrderSide orderSide,
            OrderChannel orderChannel,
            Integer number,
            Integer size,
            boolean isAscending) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosTrade, List<PosTrade>>() {
                    @Override
                    public List<PosTrade> query() {
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                createPredicatesOfFindByCondition(
                                        criteriaBuilder,
                                        root,
                                        symbolId,
                                        null,
                                        userIds,
                                        exceptUserIds,
                                        id,
                                        idFrom,
                                        idTo,
                                        dateFrom,
                                        dateTo,
                                        tradeAction,
                                        orderSide,
                                        orderType,
                                        null,
                                        null,
                                        orderChannel,
                                        null,
                                        null));
                    }
                });
    }

    // BO管理画面専用、orderChannel検索条件が必要、さらに、ユーザーIDが複数入力可能です。
    public List<PosTrade> findAllFromHistoryForBo(
            Symbol symbol,
            List<Long> userIds,
            List<Long> exceptUserIds,
            Long id,
            Long idFrom,
            Long idTo,
            Date createdAtFrom,
            Date createdAtTo,
            Long userId,
            TradeAction tradeAction,
            OrderSide orderSide,
            OrderType orderType,
            OrderChannel orderChannel) {
        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();

        StringBuilder sql =
                createSqlString(
                        mapSqlParameterSource,
                        symbol,
                        userId,
                        userIds,
                        exceptUserIds,
                        id,
                        idFrom,
                        idTo,
                        orderSide,
                        orderType,
                        null,
                        null,
                        orderChannel,
                        null,
                        null,
                        null,
                        tradeAction,
                        null,
                        null,
                        createdAtFrom,
                        createdAtTo,
                        null,
                        null);

        log.info("redshift-sql:" + sql.toString());
        log.info("redshift-params:" + mapSqlParameterSource.getValues().toString());

        return historicalTransactionManager.findFromHistory(
                sql.toString(), mapSqlParameterSource, newRowMapper());
    }

    // ユーザー別約定件数カウントクエリ
    private List<Object[]> countPosTradesByUserByConditionQuery(
            EntityManager entityManager,
            CriteriaBuilder criteriaBuilder,
            CriteriaQuery<Object[]> criteriaQuery,
            Root<PosTrade> root,
            List<Predicate> predicates) {

        /** group by */
        criteriaQuery.groupBy(root.get(Trade_.userId));

        /** select */
        criteriaQuery.multiselect(
                // ユーザーID
                root.get(Trade_.userId),
                criteriaBuilder.count(root.get(Trade_.id)).as(Integer.class));

        /** where */
        if (!CollectionUtils.isEmpty(predicates)) {
            criteriaQuery.where(predicates.toArray(new Predicate[] {}));
        }
        try {
            return entityManager.createQuery(criteriaQuery).getResultList();
        } catch (Exception e) {
            return null;
        }
    }

    public PageData<PosTrade> createPageData(
            List<PosTrade> content, Long count, Integer number, Integer size) {
        List<PosTrade> pageContents = new ArrayList<PosTrade>();

        int maxSize =
                (number * size + size) > content.size() ? content.size() : (number * size + size);

        for (int i = number * size; i < maxSize; i++) {

            pageContents.add(content.get(i));
        }

        return new PageData<PosTrade>(number, size, count, pageContents);
    }

    public void archive(PosTrade posTrade) {
        String sql = "UPDATE pos_trade SET notes = ? WHERE id = ?";
        log.info("update_archive_log_pos_trade, {}", sql);
        PreparedStatementSetter preparedStatementSetter =
                ps -> {
                    ps.setString(1, posTrade.getNotes());
                    ps.setLong(2, posTrade.getId());
                };
        historicalTransactionManager.archiveWithParameter(sql, preparedStatementSetter);
    }

    public void archive(Symbol symbol) {
        final int archiveSizeLimit = 1000;

        // id昇順ソート済み
        List<PosTrade> posTrades = findArchiveTarget(symbol, archiveSizeLimit);

        if (CollectionUtils.isEmpty(posTrades)) {
            return;
        }

        // 処理：最大idをarchive(削除)対象から除外する ※事前にid昇順ソート済み
        // 目的：Aurora fail over時のauto-increment値がPKの最大ID+1にリセットされる問題対応のため、
        // 削除時に最大IDを1件処理せずに残しておく
        posTrades.remove(posTrades.size() - 1);

        if (CollectionUtils.isEmpty(posTrades)) {
            return;
        }

        List<Long> ids = new ArrayList<>(posTrades.size());
        String insertSQL =
                "INSERT INTO pos_trade ("
                        + "id, symbol_id, user_id, order_side, order_type, order_channel, "
                        + "price, amount, jpy_conversion, trade_action, order_id, fee, asset_amount, id_type,"
                        + "eval_profit_loss_amt, eval_profit_loss_amt_rate, avg_acq_unit_price, income, "
                        + "experience_points, notes, user_growth_stage_id, created_at, updated_at"
                        + ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

        historicalTransactionManager.batchUpdate(
                insertSQL,
                new BatchPreparedStatementSetter() {
                    @Override
                    public void setValues(PreparedStatement ps, int i) throws SQLException {
                        PosTrade posTrade = posTrades.get(i);
                        ps.setLong(1, posTrade.getId());
                        ps.setLong(2, posTrade.getSymbolId());
                        ps.setLong(3, posTrade.getUserId());
                        ps.setString(4, posTrade.getOrderSide().name());
                        ps.setString(5, posTrade.getOrderType().name());
                        ps.setString(6, posTrade.getOrderChannel().name());
                        ps.setBigDecimal(7, posTrade.getPrice());
                        ps.setBigDecimal(8, posTrade.getAmount());
                        ps.setBigDecimal(9, posTrade.getJpyConversion());
                        ps.setString(10, posTrade.getTradeAction().name());
                        ps.setLong(11, posTrade.getOrderId());
                        ps.setBigDecimal(12, posTrade.getFee());
                        ps.setBigDecimal(13, posTrade.getAssetAmount());
                        ps.setString(14, posTrade.getIdType().name());
                        ps.setBigDecimal(15, posTrade.getEvalProfitLossAmt());
                        ps.setBigDecimal(16, posTrade.getEvalProfitLossAmtRate());
                        ps.setBigDecimal(17, posTrade.getAvgAcqUnitPrice());
                        ps.setBigDecimal(18, posTrade.getIncome());
                        ps.setObject(19, posTrade.getExperiencePoints(), Types.INTEGER);
                        ps.setString(20, posTrade.getNotes());
                        ps.setObject(21, posTrade.getUserGrowthStageId(), Types.INTEGER);
                        ps.setObject(
                                22,
                                FormatUtil.format(
                                        posTrade.getCreatedAt(),
                                        FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SS_S));
                        ps.setObject(
                                23,
                                posTrade.getUpdatedAt() != null
                                        ? FormatUtil.format(
                                                posTrade.getUpdatedAt(),
                                                FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SS_S)
                                        : null);

                        ids.add(posTrade.getId());
                    }

                    @Override
                    public int getBatchSize() {
                        return posTrades.size();
                    }
                });

        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
        mapSqlParameterSource.addValue("ids", ids);
        customTransactionManager.multiUpdate(
                "delete from pos_trade where id in (:ids)", mapSqlParameterSource);
        log.info("archive_log_pos_trade,{}", ids);
    }

    List<PosTrade> findArchiveTarget(Symbol symbol, int archiveSizeLimit) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosTrade, List<PosTrade>>() {
                    @Override
                    public List<PosTrade> query() {
                        Calendar calendar = Calendar.getInstance();
                        calendar.add(Calendar.DATE, -2);

                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalSymbolId(criteriaBuilder, root, symbol.getId()));
                        predicates.add(
                                predicate.lessThanCreatedAt(
                                        criteriaBuilder, root, calendar.getTime()));
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                0,
                                archiveSizeLimit,
                                criteriaBuilder.asc(root.get(Trade_.id)));
                    }
                });
    }

    @Override
    public void redisPublish(PosTrade entity) {
        redisPublisher.publish(entity);
    }

    public List<PosTrade> getPosTradeHistoryTotals(
            Long symbolId, Long userId, OrderSide orderSide, UserIdType idType) {
        String sql =
                "select * "
                        + "from pos_trade "
                        + "where symbol_id = :symbolId "
                        + "and user_id = :userId "
                        + "and order_side = :orderSide "
                        + "and id_type = :idType";

        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
        mapSqlParameterSource.addValue("symbolId", symbolId);
        mapSqlParameterSource.addValue("userId", userId);
        mapSqlParameterSource.addValue("orderSide", orderSide);
        mapSqlParameterSource.addValue("idType", idType);

        log.info("sql:{}", sql);

        return historicalTransactionManager.findFromHistory(
                sql.toString(), mapSqlParameterSource, newRowMapper());
    }

    public List<PosTrade> getPosTradeAmountTotal(
            Long symbolId, Long userId, OrderSide orderSide, UserIdType idType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosTrade, List<PosTrade>>() {
                    @Override
                    public List<PosTrade> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
                        predicates.add(predicate.equalIdType(criteriaBuilder, root, idType));

                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.asc(root.get(Trade_.id)));
                    }
                });
    }

    public PosTrade getLastPrice(
            Long symbolId, Long userId, OrderSide orderSide, UserIdType idType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosTrade, PosTrade>() {
                    @Override
                    public PosTrade query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
                        predicates.add(predicate.equalIdType(criteriaBuilder, root, idType));
                        return getSingleResult(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.desc(root.get(Trade_.updatedAt)) // 按 updatedAt 降序排序
                                );
                    }
                });
    }

    public PosTrade getLastPriceFromHistory(
            Long symbolId, Long userId, OrderSide orderSide, UserIdType idType) {
        String sql =
                "select * from pos_trade "
                        + "where symbol_id = :symbolId "
                        + "and user_id = :userId "
                        + "and order_side = :orderSide "
                        + "and id_type = :idType "
                        + "order by updated_at DESC "
                        + "limit 1";
        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
        mapSqlParameterSource.addValue("symbolId", symbolId);
        mapSqlParameterSource.addValue("userId", userId);
        mapSqlParameterSource.addValue("orderSide", orderSide);
        mapSqlParameterSource.addValue("idType", idType);
        log.info("sql:{}", sql);
        try {
            return historicalTransactionManager.findSingleFromHistoryWithMultipleConditions(
                    sql, mapSqlParameterSource, new PosTradeRowMapper());
        } catch (EmptyResultDataAccessException e) {
            log.info("No record found for the given parameters.");
            return null;
        }
    }

    public List<PosTrade> findByUserIdAndOrderSide(Long userId, OrderSide orderSide) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosTrade, List<PosTrade>>() {
                    @Override
                    public List<PosTrade> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<PosTrade> findHistoryByUserIdAndOrderSide(Long userId, OrderSide orderSide) {
        StringBuilder sql =
                new StringBuilder(
                        "select * from pos_trade where user_id = :userId and order_side = :orderSide");
        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
        mapSqlParameterSource.addValue("userId", userId);
        mapSqlParameterSource.addValue("orderSide", orderSide);
        log.info("sql:{}", sql);
        return historicalTransactionManager.findFromHistory(
                sql.toString(), mapSqlParameterSource, newRowMapper());
    }

    public List<PosTrade> findAllByUserIdAndOrderSide(Long userId, OrderSide orderSide) {
        List<PosTrade> posTrades =
                Optional.ofNullable(this.findByUserIdAndOrderSide(userId, orderSide))
                        .orElseGet(ArrayList::new);
        List<PosTrade> posTradesHistory = this.findHistoryByUserIdAndOrderSide(userId, orderSide);
        if (!CollectionUtils.isEmpty(posTradesHistory)) {
            posTrades.addAll(posTradesHistory);
        }
        return posTrades;
    }

    public PageData<MonsterCropsEatenResponse> getCropsEatenByCondition(
            Long userId, Integer number, Integer size) {

        int offset = (number - 1) * size;
        // mysql
        String sql =
                "SELECT "
                        + "ROW_NUMBER() OVER (ORDER BY symbol_id) AS id, "
                        + "symbol_id AS symbolId, "
                        + "count(*) AS sumAmount, "
                        + "SUM(experience_points) AS sumExperience, "
                        + "SUM(FLOOR(income)) AS sumIncome "
                        + "FROM pos_trade "
                        + "WHERE user_id = :userId AND order_side = 'SELL' "
                        + "GROUP BY symbol_id "
                        + "ORDER BY sumAmount DESC "
                        + "LIMIT :limit OFFSET :offset";
        MapSqlParameterSource parameters =
                new MapSqlParameterSource()
                        .addValue("userId", userId)
                        .addValue("limit", size)
                        .addValue("offset", offset);

        Pair<Integer, Integer> totalElements = getTotalElements(userId);
        try {
            List<Map<String, Object>> resultList =
                    Optional.of(namedParameterJdbcTemplate.queryForList(sql, parameters))
                            .orElse(Collections.emptyList());
            int resultCount = resultList.size();
            // redshift
            /*
               resultCount = size
               return redshift => size-resultCount

               resultCount < size
               redshift => size-resultCount
               return  mysql => resultCount + redshift => size-resultCount

               I don't understand why GROUP BY & SUM still require pagination, but I respect it.
            */
            if (resultCount < size) {
                Integer newSize = size - resultCount;
                int newOffset = number - (totalElements.getFirst() / size) - 1;
                MapSqlParameterSource historyParameters =
                        new MapSqlParameterSource()
                                .addValue("userId", userId)
                                .addValue("limit", newSize)
                                .addValue("offset", newOffset);
                List<Map<String, Object>> histories =
                        historicalTransactionManager.queryForListFromHistory(
                                sql, historyParameters);
                Map<Object, Map<String, Object>> mergedMap = new HashMap<>();
                mergeMapsIntoGroup(mergedMap, resultList);
                mergeMapsIntoGroup(mergedMap, histories);
                resultList = new ArrayList<>(mergedMap.values());
            }
            return new PageData<>(
                    number,
                    size,
                    totalElements.getSecond(),
                    convertToMonsterFoodHistoryResponseList(resultList));
        } catch (EmptyResultDataAccessException e) {
            return new PageData<>(number, size, 0, null);
        }
    }

    private void mergeMapsIntoGroup(
            Map<Object, Map<String, Object>> mergedMap, List<Map<String, Object>> mapsToMerge) {
        for (Map<String, Object> map : mapsToMerge) {
            Object symbolId =
                    map.containsKey("symbolId")
                            ? map.get("symbolId")
                            : map.getOrDefault("symbolid", null);
            if (symbolId == null) continue;

            Map<String, Object> mergedItem =
                    mergedMap.computeIfAbsent(symbolId, k -> new HashMap<>());
            if (!mergedItem.containsKey("id")) {
                mergedItem.put("id", map.get("id"));
            }
            mergedItem.putIfAbsent("symbolId", symbolId);

            addNumericField(mergedItem, map, "sumAmount", "sumamount");
            addNumericField(mergedItem, map, "sumExperience", "sumexperience");
            addNumericField(mergedItem, map, "sumIncome", "sumincome");
        }
    }

    private static void addNumericField(
            Map<String, Object> target,
            Map<String, Object> source,
            String primaryKey,
            String alternateKey) {
        Object sourceValue = source.get(primaryKey);
        if (sourceValue == null) {
            sourceValue = source.get(alternateKey);
            if (sourceValue == null) return;
        }
        Object targetValue = target.get(primaryKey);
        if (targetValue == null) {
            target.put(primaryKey, sourceValue);
        } else if (sourceValue instanceof Number && targetValue instanceof Number) {
            BigDecimal result =
                    new BigDecimal(targetValue.toString())
                            .add(new BigDecimal(sourceValue.toString()));
            if (targetValue instanceof Long) {
                target.put(primaryKey, result.longValue());
            } else if (targetValue instanceof Integer) {
                target.put(primaryKey, result.intValue());
            } else if (targetValue instanceof BigDecimal) {
                target.put(primaryKey, result);
            } else {
                target.put(primaryKey, result);
            }
        } else {
            target.put(primaryKey, sourceValue);
        }
    }

    private Pair<Integer, Integer> getTotalElements(Long userId) {
        // mysql
        String countSql =
                "SELECT DISTINCT symbol_id "
                        + "FROM pos_trade "
                        + "WHERE user_id = :userId AND order_side = 'SELL'";
        MapSqlParameterSource countParameters =
                new MapSqlParameterSource().addValue("userId", userId);

        List<Long> result =
                namedParameterJdbcTemplate.queryForList(countSql, countParameters, Long.class);
        // redshift
        List<Long> resultFromHistory =
                historicalJdbcTemplate.queryForList(countSql, countParameters, Long.class);
        // merge
        List<Long> mergedList =
                Stream.concat(result.stream(), resultFromHistory.stream()).distinct().toList();
        return Pair.of(result.size(), mergedList.size());
    }

    private List<MonsterCropsEatenResponse> convertToMonsterFoodHistoryResponseList(
            List<Map<String, Object>> resultList) {
        return resultList.stream()
                .map(
                        map -> {
                            MonsterCropsEatenResponse response = new MonsterCropsEatenResponse();
                            response.setId(map.get("id").toString());
                            response.setCourse(
                                    CurrencyTypeMonster.getMonsterCurrencyTypeById(
                                            (Long) map.get("symbolId")));
                            response.setCropName(
                                    CurrencyTypeMonster.getMonsterFruitNameById(
                                            (Long) map.get("symbolId")));
                            response.setSumAmont((Long) map.get("sumAmount"));
                            response.setSumExperience(
                                    new BigDecimal(map.get("sumExperience").toString()));
                            response.setSumIncome(new BigDecimal(map.get("sumIncome").toString()));
                            return response;
                        })
                .toList();
    }
}
