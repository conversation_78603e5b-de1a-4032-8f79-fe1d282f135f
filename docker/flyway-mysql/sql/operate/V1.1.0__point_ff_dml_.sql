update admin_menu
set menu_name = '投票設定一覧', updated_at = now()
where menu_name = 'Choice設定一覧';

update admin_menu
set menu_name = '投票結果一覧', updated_at = now()
where menu_name = 'Choice結果一覧';

-- OFLM-2001
-- fix data
update quiz_question_published_record
set random_answer = 'A', answer_mapping = 'A:A', updated_at = now()
where created_at < now();

-- fix data
UPDATE quiz_user_answer a
    JOIN quiz_question_published_record p ON p.quiz_question_id = a.quiz_id
    AND p.published_date = a.answer_date
    SET a.published_record_id = p.id, original_answer = correct_answer,
        a.updated_at = NOW()
WHERE  a.updated_at < now();

-- OFLM-1976
update admin_menu
set menu_name = 'ポイント交換一覧', updated_at = now()
where menu_name = '運用ポイント管理';

update admin_menu
set menu_name = '牧場⇒Ponta_交換一覧', updated_at = now()
where menu_name = '引き出し一覧';

update admin_menu
set menu_name = 'Pont<PERSON>⇒牧場_交換一覧', updated_at = now()
where menu_name = '振替一覧';