package point.worker.worker;

import static point.common.entity.PontaHulftTaskStatus.ParsingStatus.PENDING;
import static point.common.entity.PontaHulftTaskStatus.ParsingStatus.SUCCESS;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigInteger;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import point.common.component.CustomTransactionManager;
import point.common.constant.HulftUploadStatusEnum;
import point.common.constant.PointTransferStatusEnum;
import point.common.constant.PointTransferTypeEnum;
import point.common.constant.UserIdType;
import point.common.entity.PointTransfer;
import point.common.entity.PontaHulftTaskStatus;
import point.common.entity.Symbol;
import point.common.ponta.PointTransactionBuilder;
import point.common.ponta.TradeNumberGenerator;
import point.common.service.PointTransferService;
import point.common.service.PontaHufltTaskStatusService;
import point.common.util.DateUnit;
import point.worker.component.Worker;

@Slf4j
@Component
@RequiredArgsConstructor
public class PontaGrantPointHulftUpload extends Worker {

    private final PointTransferService pointTransferService;

    private final CustomTransactionManager customTransactionManager;

    private final PontaHufltTaskStatusService pontaHufltTaskStatusService;

    @Value("${hulft.snddata}")
    private String hulftSnddata;

    @Value("${hulft.file_name_dist}")
    private String fileName;

    private static final String INTERFACE_ID = "0007"; // Interface ID
    private static final String FROM_CUST_CODE = "106242"; // Customer code (From)
    private static final int LINE_BREAK_CODE = 426; // 改行コード
    private final TradeNumberGenerator tradeNumberGenerator;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info("----------------- Start ----------------");
        try {
            customTransactionManager.execute(
                    entityManager -> {
                        PontaHulftTaskStatus pontaHufltTask =
                                pontaHufltTaskStatusService.findByCondition();
                        if (pontaHufltTask != null
                                && pontaHufltTask.getParsingStatus() != null
                                && !SUCCESS.equals(pontaHufltTask.getParsingStatus())) {
                            log.error(
                                    "if008 Previous task not completed successfully, skipping execution , taskStatus: {}, updatedAt: {}",
                                    pontaHufltTask.getParsingStatus(),
                                    pontaHufltTask.getUpdatedAt());
                            return;
                        }
                        if (pontaHufltTask == null) {
                            pontaHufltTask = new PontaHulftTaskStatus();
                            pontaHufltTask.setTaskName(fileName);
                        }
                        List<PointTransfer> transferList = fetchTransferList();
                        if (transferList == null || transferList.isEmpty()) {
                            log.info("Failed to fetch transfer list, skipping execution");
                        }
                        int count = 0;
                        if (transferList != null && !transferList.isEmpty()) {
                            count = transferList.size();
                        }
                        Path filePath = prepareFile();
                        if (filePath == null) {
                            log.error("Failed to prepare file, skipping execution");
                            return;
                        }

                        writeHeader(filePath, count);
                        if (transferList != null && !transferList.isEmpty()) {
                            writeBody(filePath, transferList, pontaHufltTask, entityManager);
                        }
                        writeFooter(filePath);
                        pontaHufltTask.setParsingStartTime(new Date());
                        pontaHufltTask.setParsingStatus(PENDING);
                        pontaHufltTaskStatusService.save(pontaHufltTask, entityManager);
                        String fileContent =
                                Files.readString(filePath, Charset.forName("Shift_JIS"));
                        log.info("hulft File content: {}", fileContent);
                    });
        } catch (IOException e) {
            log.error("File generation failed, skipping upload: {}", e.getMessage(), e);
        }
    }

    private List<PointTransfer> fetchTransferList() {
        PointTransferStatusEnum processing = PointTransferStatusEnum.PROCESSING;
        PointTransferTypeEnum out = PointTransferTypeEnum.OUT;
        UserIdType operate = UserIdType.Operate;
        HulftUploadStatusEnum pending = HulftUploadStatusEnum.PENDING;
        return pointTransferService.findByCondition(null, processing, out, operate, pending);
    }

    private Path prepareFile() {
        try {
            Path directory = Paths.get(hulftSnddata);
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
                log.info("Directory created successfully: {}", hulftSnddata);
            }
            return directory.resolve(fileName);
        } catch (IOException e) {
            log.error("Failed to prepare file directory: {}", e.getMessage());
            return null;
        }
    }

    private void writeHeader(Path filePath, int transferCount) throws IOException {
        String yyyyMMdd = DateUnit.getYYMMDDString(LocalDate.now());
        String hhMMSS = DateUnit.getHHMMSSString(LocalDateTime.now());
        String header = buildHeader(yyyyMMdd, hhMMSS, transferCount);
        log.info("header_length:{}, header_content: {}", header.length(), header);
        writeToFile(filePath, header, false);
    }

    // 明細レコード
    private void writeBody(
            Path filePath,
            List<PointTransfer> transferList,
            PontaHulftTaskStatus pontaHufltTaskStatus,
            EntityManager entityManager)
            throws Exception {
        String yyyyMMdd = DateUnit.getYYMMDDString(LocalDate.now());
        String hhMMSS = DateUnit.getHHMMSSString(LocalDateTime.now());
        Date date = new Date();
        StringBuilder sb = new StringBuilder();
        String requestData = "";
        for (PointTransfer pointTransfer : transferList) {
            String partnerMemberId = pointTransfer.getPointUser().getPartnerMemberId();
            String tradeNumber = pointTransfer.getTradeNumber();
            BigInteger amount = pointTransfer.getAmount().toBigInteger();
            pointTransfer.setUploadTime(date);
            pointTransfer.setUploadStatus(HulftUploadStatusEnum.PROCESSING);
            pointTransferService.save(pointTransfer, entityManager);
            log.info("tradeNumber: {}, partnerMemberId: {}", tradeNumber, partnerMemberId);
            String body =
                    PointTransactionBuilder.buildTransactionString(
                            yyyyMMdd,
                            hhMMSS,
                            FROM_CUST_CODE,
                            partnerMemberId,
                            tradeNumber,
                            String.format("%08d", amount));
            requestData = unescapeUnicode(body);
            log.info("hulft requestData_content:{}", requestData.length());
            sb.append(requestData);
            log.info("Generated requestData: {}", requestData);
            writeToFile(filePath, requestData, true);
        }
        pontaHufltTaskStatus.setSendContent(sb.toString());
    }

    private void writeFooter(Path filePath) throws IOException {
        String yyyyMMdd = DateUnit.getYYMMDDString(LocalDate.now());
        String hhMMSS = DateUnit.getHHMMSSString(LocalDateTime.now());
        String footer = buildFooter(yyyyMMdd, hhMMSS);
        log.info("footer_length: {}, footer_content:{}", footer.length(), footer);
        writeToFile(filePath, footer, true);
    }

    // ヘッダレコード 20040430(処理日) ・091538（処理時間）・ 000000000003（データ件数）件 ・ 以降改行コードを除いたバイト長分を固定でALL-半角SPACE
    private String buildHeader(String yyyyMMdd, String hhMMSS, int transferCount) {
        String headerBase =
                INTERFACE_ID + "00101" + yyyyMMdd + hhMMSS + String.format("%012d", transferCount);
        int headerBaseLength = headerBase.length();
        int headerPaddingLength = LINE_BREAK_CODE - headerBaseLength;
        return headerBase + unescapeUnicode("\\u0020").repeat(headerPaddingLength) + "\n";
    }

    // トレーラレコード 20040430(処理日) ・091538（処理時間）・ALL-半角SPACE
    private String buildFooter(String yyyyMMdd, String hhMMSS) {
        String base = INTERFACE_ID + "00103" + yyyyMMdd + hhMMSS;
        int baseLength = base.length();
        int paddingLength = LINE_BREAK_CODE - baseLength;
        return base + unescapeUnicode("\\u0020").repeat(paddingLength) + "\n";
    }

    private void writeToFile(Path filePath, String content, boolean append) throws IOException {
        try (OutputStreamWriter writer =
                new OutputStreamWriter(
                        new FileOutputStream(filePath.toFile(), append), "Shift_JIS")) {
            writer.write(content);
            log.info("File written successfully: {}", filePath);
        }
    }

    public static String unescapeUnicode(String input) {
        Pattern pattern = Pattern.compile("\\\\u([0-9a-fA-F]{4})");
        Matcher matcher = pattern.matcher(input);
        StringBuilder sb = new StringBuilder();
        while (matcher.find()) {
            String group = matcher.group(1);
            char ch = (char) Integer.parseInt(group, 16);
            matcher.appendReplacement(sb, Character.toString(ch));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
}
