package point.app.operate.controller.pontaApi;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import point.app.component.model.UserPrincipal;
import point.common.constant.BizCode;
import point.common.constant.ErrorCode;
import point.common.constant.PointTransferTypeEnum;
import point.common.constant.ViewVariables;
import point.common.entity.PointTransfer;
import point.common.exception.GameException;
import point.common.model.request.PointForm;
import point.common.model.response.GlobalApiResponse;
import point.common.model.response.PageData;
import point.common.model.response.PointTransferResponse;
import point.common.ponta.PontaBizInVokerApi;
import point.common.service.PointTransferService;

@RestController
@RequestMapping("/app/v1/operate/point")
@RequiredArgsConstructor
public class OperatePointController {

    private final PointTransferService pointTransferService;
    private static final String SUCCESS_CODE_VALUE = "N000000000";
    private static final String ERROR_CODE_N058 = "N058";
    private static final String ERROR_CODE_G000 = "G000";
    private static final String ERROR_CODE_KEY = "E000000000";

    @Value("${ponta.transfer-fee:0.01}")
    private BigDecimal feePercent;

    /** PONTAポイント→運用ポイント 利用 IF-003 ポイント付与利用（上り） */
    @PostMapping("/ponta-to-operation")
    @Operation(
            summary = "Convert ponta to operation",
            security = @SecurityRequirement(name = "x-auth"),
            requestBody =
                    @io.swagger.v3.oas.annotations.parameters.RequestBody(
                            description = "Point form data",
                            required = true,
                            content = @Content(schema = @Schema(implementation = PointForm.class))),
            responses = {@ApiResponse(responseCode = "200", description = "Success")})
    public ResponseEntity<GlobalApiResponse<Object>> pontaToOperation(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user,
            @RequestBody PointForm pointForm)
            throws Exception {

        if (user == null) {
            return ResponseEntity.ok()
                    .body(
                            new GlobalApiResponse<>(
                                    ErrorCode.GAME_USER_NOT_FOUND.getCode(),
                                    ErrorCode.GAME_USER_NOT_FOUND.getMessage()));
        }

        // pointAmount が null かどうかを検証
        if (pointForm.getPointAmount() == null) {
            throw new GameException(
                    ErrorCode.PONTA_POINTS_CANNOT_BE_NULL,
                    ErrorCode.PONTA_POINTS_CANNOT_BE_NULL.getMessage());
        }

        // pointAmount が負の数かどうかを検証
        if (pointForm.getPointAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new GameException(
                    ErrorCode.PONTA_INVALID_NEGATIVE_PARAMETER_ERROR,
                    ErrorCode.PONTA_INVALID_NEGATIVE_PARAMETER_ERROR.getMessage());
        }
        Long partnerId = user.getUserWrapper().getPointUser().getPartnerId();
        // 変換ロジックを実行
        String responseCode =
                pointTransferService.transferPontaToOperation(
                        user.getId(),
                        partnerId,
                        user.getUserIdType(),
                        pointForm.getPointAmount(),
                        PointTransferTypeEnum.IN);
        if (ERROR_CODE_KEY.equals(responseCode)) {
            return ResponseEntity.ok()
                    .body(
                            new GlobalApiResponse<>(
                                    HttpStatus.OK.value(),
                                    ErrorCode.PONTA_REQUEST_ERROR_CODE.getMessage()));
        }
        if (SUCCESS_CODE_VALUE.equals(responseCode)) {
            return ResponseEntity.ok()
                    .body(
                            new GlobalApiResponse<>(
                                    HttpStatus.OK.value(),
                                    BizCode.PONTA_POINT_EXCHANGE_SUCCESS.getMessage()));
        } else if (responseCode.startsWith(ERROR_CODE_N058)
                || responseCode.startsWith(ERROR_CODE_G000)) {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.PONTA_RESPONSE_SUCCESS_CODE_N058_G000.getMessage()));
        } else {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.PONTA_RESPONSE_ERROR_CODE.getMessage(
                                            PontaBizInVokerApi.extractErrorPrefix(responseCode))));
        }
    }

    /** 運用ポイント→PONTAポイント 付与 IF-003 ポイント付与利用（上り） */
    @PostMapping("/operation-to-ponta")
    @Operation(
            summary = "Convert operation to ponta",
            security = @SecurityRequirement(name = "x-auth"),
            requestBody =
                    @io.swagger.v3.oas.annotations.parameters.RequestBody(
                            description = "Point form data",
                            required = true,
                            content = @Content(schema = @Schema(implementation = PointForm.class))),
            responses = {@ApiResponse(responseCode = "200", description = "Success")})
    public ResponseEntity<GlobalApiResponse<Object>> operationToPonta(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user,
            @RequestBody PointForm pointForm)
            throws Exception {

        if (user == null) {
            return ResponseEntity.ok()
                    .body(
                            new GlobalApiResponse<>(
                                    ErrorCode.GAME_USER_NOT_FOUND.getCode(),
                                    ErrorCode.GAME_USER_NOT_FOUND.getMessage()));
        }
        // pointAmount が null かどうかを検証
        if (pointForm.getPointAmount() == null) {
            throw new GameException(
                    ErrorCode.PONTA_POINTS_CANNOT_BE_NULL,
                    ErrorCode.PONTA_POINTS_CANNOT_BE_NULL.getMessage());
        }

        // pointAmount が負の数かどうかを検証
        if (pointForm.getPointAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new GameException(
                    ErrorCode.PONTA_INVALID_NEGATIVE_PARAMETER_ERROR,
                    ErrorCode.PONTA_INVALID_NEGATIVE_PARAMETER_ERROR.getMessage());
        }

        // check input fee
        BigDecimal calculatedFee =
                pointForm
                        .getPointAmount()
                        .add(pointForm.getPointAmountFee())
                        .multiply(feePercent)
                        .setScale(0, RoundingMode.FLOOR);
        validateFeeValue(pointForm.getPointAmountFee(), calculatedFee);

        Long partnerId = user.getUserWrapper().getPointUser().getPartnerId();
        // 変換ロジックを実行
        pointTransferService.transferOperationToPonta(
                user.getId(),
                partnerId,
                user.getUserIdType(),
                pointForm.getPointAmount(),
                pointForm.getPointAmountFee(),
                PointTransferTypeEnum.OUT);
        return ResponseEntity.ok()
                .body(
                        new GlobalApiResponse<>(
                                HttpStatus.OK.value(),
                                BizCode.PONTA_POINT_EXCHANGE_SUCCESS.getMessage()));
    }

    /** ポイント交換履歴 */
    @GetMapping("/exchange-history")
    @Operation(
            summary = "get exchange history",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {@ApiResponse(responseCode = "200", description = "Success")})
    public ResponseEntity<GlobalApiResponse<PageData<PointTransferResponse>>> getExchangeHistory(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {
        if (user == null) {
            return ResponseEntity.ok()
                    .body(
                            new GlobalApiResponse<>(
                                    ErrorCode.GAME_USER_NOT_FOUND.getCode(),
                                    ErrorCode.GAME_USER_NOT_FOUND.getMessage()));
        }
        List<PointTransferResponse> pointTransferResponseList = new ArrayList<>();
        PageData<PointTransfer> pageData =
                pointTransferService.findByConditionPage(user.getId(), number, size);
        pageData.getContent()
                .forEach(
                        pointTransfer -> {
                            pointTransferResponseList.add(
                                    PointTransferResponse.builder()
                                            .id(pointTransfer.getId())
                                            .userId(pointTransfer.getUserId())
                                            .userIdType(pointTransfer.getUserIdType().toString())
                                            .transferType(
                                                    pointTransfer.getTransferType().toString())
                                            .amount(pointTransfer.getAmount())
                                            .status(pointTransfer.getStatus().getCode())
                                            .requestTime(pointTransfer.getRequestTime())
                                            .transferTime(pointTransfer.getTransferTime())
                                            .createdAt(pointTransfer.getCreatedAt())
                                            .build());
                        });
        return ResponseEntity.ok()
                .body(
                        new GlobalApiResponse<>(
                                HttpStatus.OK.value(),
                                new PageData<>(
                                        number,
                                        size,
                                        pageData.getTotalElements(),
                                        pointTransferResponseList)));
    }

    private static void validateFeeValue(BigDecimal inputFee, BigDecimal calculatedFee)
            throws GameException {
        // fee not null
        if (Objects.isNull(inputFee) || inputFee.compareTo(BigDecimal.ZERO) < 0) {
            throw new GameException(
                    ErrorCode.PONTA_TRANSFER_FEE_ERROR_CODE,
                    ErrorCode.PONTA_TRANSFER_FEE_ERROR_CODE.getMessage());
        }
        // check fee
        if (inputFee.compareTo(calculatedFee) != 0) {
            throw new GameException(
                    ErrorCode.PONTA_TRANSFER_FEE_ERROR_CODE,
                    ErrorCode.PONTA_TRANSFER_FEE_ERROR_CODE.getMessage());
        }
    }
}
