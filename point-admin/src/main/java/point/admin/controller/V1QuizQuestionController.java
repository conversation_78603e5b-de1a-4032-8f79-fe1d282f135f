package point.admin.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import point.admin.entity.AdminUser;
import point.common.constant.ViewVariables;
import point.common.entity.QuizQuestion;
import point.common.exception.CustomException;
import point.common.model.request.QuizQuestionEnableForm;
import point.common.model.request.QuizQuestionForm;
import point.common.model.response.PageData;
import point.common.model.response.QuizQuestionPublishedRecordData;
import point.common.model.response.QuizQuestionPublishedRecordResponse;
import point.common.service.QuizQuestionService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/quiz")
@PreAuthorize("@auth.check('quiz-list')")
public class V1QuizQuestionController {

    private final QuizQuestionService quizQuestionService;

    @GetMapping()
    public ResponseEntity<PageData<QuizQuestion>> save(
            @RequestParam(value = "quizNum", required = false) String quizNum,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "publishedFrom", required = false) Long publishedFrom,
            @RequestParam(value = "publishedTo", required = false) Long publishedTo,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number)
            throws CustomException {
        return ResponseEntity.ok(
                quizQuestionService.findByPage(
                        size, number, quizNum, title, publishedFrom, publishedTo));
    }

    @PostMapping("")
    public ResponseEntity<Void> save(
            @AuthenticationPrincipal AdminUser adminUser, @RequestBody QuizQuestionForm form)
            throws CustomException {
        quizQuestionService.saveQuiz(adminUser.getEmail(), form);
        return ResponseEntity.ok().build();
    }

    @PutMapping()
    public ResponseEntity<Void> edit(
            @AuthenticationPrincipal AdminUser adminUser, @RequestBody QuizQuestionForm form)
            throws CustomException {
        quizQuestionService.editQuiz(adminUser.getEmail(), form);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/status")
    public ResponseEntity<Void> switchStatus(@RequestBody QuizQuestionEnableForm form)
            throws CustomException {
        quizQuestionService.enableSwitch(form.getId(), form.isEnable());
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}")
    public ResponseEntity<QuizQuestion> getQuiz(@PathVariable Long id) throws CustomException {
        return ResponseEntity.ok(quizQuestionService.getQuestion(id));
    }

    @GetMapping("/published")
    public ResponseEntity<PageData<QuizQuestionPublishedRecordData>> test(
            @RequestParam(value = "quizNum", required = false) String quizNum,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "publishedFrom", required = false) Long publishedFrom,
            @RequestParam(value = "publishedTo", required = false) Long publishedTo,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number) {
        return ResponseEntity.ok(
                quizQuestionService.findPublishedRecordByPage(
                        size, number, quizNum, title, publishedFrom, publishedTo));
    }

    @GetMapping("/published/{id}")
    public ResponseEntity<QuizQuestionPublishedRecordResponse> getPublishedRecord(
            @PathVariable Long id) throws CustomException {
        return ResponseEntity.ok(quizQuestionService.findPublishedRecordById((id)));
    }
}
